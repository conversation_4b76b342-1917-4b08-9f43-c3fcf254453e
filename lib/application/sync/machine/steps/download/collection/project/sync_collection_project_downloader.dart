import 'package:bitacora/application/sync/machine/steps/download/collection/project/project_by_remote_id_repository_query.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/project/value/project_is_syncable.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';

class SyncCollectionProjectDownloader extends SyncCollectionDownloader {
  SyncCollectionProjectDownloader(super.params);

  @override
  SyncMetadataCollectionType get collectionType =>
      SyncMetadataCollectionType.project;

  @override
  bool get supportsBatchOperations => true;

  @override
  Future<LocalId?> translateAndSave(Map<String, dynamic> map) async {
    final project = apiTranslator.project
        .fromMap(map)
        .copyWith(isSyncable: ProjectIsSyncable(true));

    return db.project.save(db.context(), project);
  }

  @override
  Future<List<LocalId?>> translateAndSaveBatch(List<Map<String, dynamic>> maps) async {
    final projects = maps.map((map) =>
      apiTranslator.project
          .fromMap(map)
          .copyWith(isSyncable: ProjectIsSyncable(true))
    ).toList();
    return db.project.saveBatch(db.context(), projects);
  }

  @override
  Future<void> deleteArchived(RemoteId remoteId) async {
    final project = await db.query(ProjectByRemoteIdRepositoryQuery(remoteId));
    if (project != null) {
      await db.project.delete(db.context(), project.id!);
    }
  }
}
